
import { Wallet } from 'lucide-react';
import {
  Sheet,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  SheetTitle,
} from '@/components/ui/sheet';

interface Token {
  symbol: string;
  name: string;
  icon: string;
  balance: string;
  value: string;
  priceUsd: number;
}

interface BalanceSidebarProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  tokens: Token[];
}

export function BalanceSidebar({ open, onOpenChange, tokens }: BalanceSidebarProps) {
  const getTotalValue = () => {
    return tokens.reduce((total, token) => {
      const balance = parseFloat(token.balance) || 0;
      return total + (balance * token.priceUsd);
    }, 0);
  };

  const formatValue = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  };

  const formatBalance = (balance: string) => {
    const num = parseFloat(balance) || 0;
    if (num === 0) return '0';
    if (num < 0.0001) return '< 0.0001';
    return num.toLocaleString('en-US', { maximumFractionDigits: 6 });
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side="right" className="w-80 bg-gray-900 border-gray-700">
        <SheetHeader className="border-b border-gray-700 pb-4">
          <SheetTitle className="flex items-center gap-2 text-white">
            <Wallet className="h-5 w-5" />
            Your Balances
          </SheetTitle>
        </SheetHeader>

        <div className="mt-6">
          {/* Total Portfolio Value */}
          <div className="bg-gray-800/60 rounded-xl p-4 mb-6">
            <p className="text-sm text-gray-400 mb-1">Total Portfolio Value</p>
            <p className="text-2xl font-bold text-white">
              {formatValue(getTotalValue())}
            </p>
          </div>

          {/* Token List */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium text-gray-400 uppercase tracking-wide">
              Assets
            </h3>
            
            {tokens.map((token) => {
              const balance = parseFloat(token.balance) || 0;
              const totalValue = balance * token.priceUsd;
              
              return (
                <div
                  key={token.symbol}
                  className="bg-gray-800/40 rounded-lg p-4 border border-gray-700/50"
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-3">
                      <span className="text-xl">{token.icon}</span>
                      <div>
                        <p className="font-medium text-white">{token.symbol}</p>
                        <p className="text-xs text-gray-400">{token.name}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-white">
                        {formatBalance(token.balance)}
                      </p>
                      <p className="text-xs text-gray-400">
                        {formatValue(totalValue)}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex justify-between items-center text-xs">
                    <span className="text-gray-500">Price</span>
                    <span className="text-gray-300">
                      {formatValue(token.priceUsd)}
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
