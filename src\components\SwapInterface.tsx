import { useState, useEffect } from "react";
import { ArrowUpDown, Wallet } from "lucide-react";
import { Button } from "@/components/ui/button";
import { SwapCard } from "@/components/SwapCard";
import { BalanceSidebar } from "@/components/BalanceSidebar";

interface Token {
  symbol: string;
  name: string;
  icon: string;
  balance: string;
  value: string;
  priceUsd: number;
}

const tokens: Token[] = [
  {
    symbol: "WETH",
    name: "Wrapped Ethereum",
    icon: "⟠",
    balance: "0.0022",
    value: "$262,155.61",
    priceUsd: 2800,
  },
  {
    symbol: "<PERSON><PERSON>",
    name: "<PERSON><PERSON>",
    icon: "🐸",
    balance: "0",
    value: "$42,479.32",
    priceUsd: 0.0057,
  },
  {
    symbol: "USDC",
    name: "USD Coin",
    icon: "💰",
    balance: "1,250.50",
    value: "$1,250.50",
    priceUsd: 1,
  },
  {
    symbol: "BTC",
    name: "Bitcoin",
    icon: "₿",
    balance: "0.05",
    value: "$2,150.00",
    priceUsd: 43000,
  },
];

export function SwapInterface() {
  const [sellToken, setSellToken] = useState(tokens[0]);
  const [buyToken, setBuyToken] = useState(tokens[1]);
  const [sellAmount, setSellAmount] = useState("");
  const [buyAmount, setBuyAmount] = useState("");
  const [isUpdatingSell, setIsUpdatingSell] = useState(false);
  const [isBalanceSidebarOpen, setIsBalanceSidebarOpen] = useState(false);

  // Calculate exchange rate
  const getExchangeRate = () => {
    return sellToken.priceUsd / buyToken.priceUsd;
  };

  // Update buy amount when sell amount changes
  const handleSellAmountChange = (amount: string) => {
    setSellAmount(amount);
    if (amount && !isNaN(parseFloat(amount))) {
      setIsUpdatingSell(true);
      const rate = getExchangeRate();
      const calculatedBuyAmount = (parseFloat(amount) * rate).toString();
      setBuyAmount(calculatedBuyAmount);
      setIsUpdatingSell(false);
    } else {
      setBuyAmount("");
    }
  };

  // Update sell amount when buy amount changes
  const handleBuyAmountChange = (amount: string) => {
    setBuyAmount(amount);
    if (amount && !isNaN(parseFloat(amount)) && !isUpdatingSell) {
      const rate = getExchangeRate();
      const calculatedSellAmount = (parseFloat(amount) / rate).toString();
      setSellAmount(calculatedSellAmount);
    } else if (!amount) {
      setSellAmount("");
    }
  };

  // Update amounts when tokens change
  useEffect(() => {
    if (sellAmount && !isNaN(parseFloat(sellAmount))) {
      const rate = getExchangeRate();
      const calculatedBuyAmount = (parseFloat(sellAmount) * rate).toString();
      setBuyAmount(calculatedBuyAmount);
    }
  }, [sellToken, buyToken, sellAmount]);

  const handleSwapTokens = () => {
    const tempToken = sellToken;
    const tempAmount = sellAmount;
    setSellToken(buyToken);
    setBuyToken(tempToken);
    setSellAmount(buyAmount);
    setBuyAmount(tempAmount);
  };

  const handleMaxClick = () => {
    handleSellAmountChange(sellToken.balance);
  };

  // Handle sell token selection with automatic swap detection
  const handleSellTokenSelect = (token: Token) => {
    if (token.symbol === buyToken.symbol) {
      // If selecting the same token as buy token, swap them
      setSellToken(token);
      setBuyToken(sellToken);
      // Swap the amounts as well
      const tempAmount = sellAmount;
      setSellAmount(buyAmount);
      setBuyAmount(tempAmount);
    } else {
      setSellToken(token);
    }
  };

  // Handle buy token selection with automatic swap detection
  const handleBuyTokenSelect = (token: Token) => {
    if (token.symbol === sellToken.symbol) {
      // If selecting the same token as sell token, swap them
      setBuyToken(token);
      setSellToken(buyToken);
      // Swap the amounts as well
      const tempAmount = buyAmount;
      setBuyAmount(sellAmount);
      setSellAmount(tempAmount);
    } else {
      setBuyToken(token);
    }
  };

  const formatExchangeRate = () => {
    const rate = getExchangeRate();
    if (rate > 1) {
      return `1 ${sellToken.symbol} = ${rate.toLocaleString()} ${
        buyToken.symbol
      }`;
    } else {
      return `1 ${sellToken.symbol} = ${rate.toFixed(8)} ${buyToken.symbol}`;
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-white">Swap</h1>
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="icon"
            className="text-gray-400 hover:text-white hover:bg-gray-800 rounded-full"
            onClick={() => setIsBalanceSidebarOpen(true)}
          >
            <Wallet className="h-5 w-5" />
          </Button>
        </div>
      </div>

      {/* Swap Cards Container */}
      <div className="relative">
        {/* You Sell Card */}
        <SwapCard
          title="You sell"
          token={sellToken}
          amount={sellAmount}
          onAmountChange={handleSellAmountChange}
          onTokenSelect={handleSellTokenSelect}
          tokens={tokens}
          showMax
          onMaxClick={handleMaxClick}
        />

        {/* Swap Button */}
        <div className="flex justify-center -my-3 relative z-10">
          <Button
            onClick={handleSwapTokens}
            variant="ghost"
            size="icon"
            className="bg-gray-800 hover:bg-gray-700 text-white rounded-full border-4 border-gray-900 transition-all duration-200 hover:scale-110"
          >
            <ArrowUpDown className="h-4 w-4" />
          </Button>
        </div>

        {/* You Buy Card */}
        <SwapCard
          title="You buy"
          token={buyToken}
          amount={buyAmount}
          onAmountChange={handleBuyAmountChange}
          onTokenSelect={handleBuyTokenSelect}
          tokens={tokens}
          className="border-2 border-teal-500/30 bg-gray-800/60"
        />
      </div>

      {/* Exchange Rate */}
      <div className="mt-4 p-3 bg-gray-800/40 rounded-lg border border-gray-700">
        <span className="w-full justify-between text-sm text-gray-300 p-2">
          {formatExchangeRate()}
        </span>
      </div>

      {/* Action Button */}
      <Button className="w-full mt-6 bg-white hover:bg-gray-100 text-black font-medium py-3 rounded-xl transition-all duration-200 hover:scale-[1.02]">
        Swap
      </Button>

      {/* Balance Sidebar */}
      <BalanceSidebar
        open={isBalanceSidebarOpen}
        onOpenChange={setIsBalanceSidebarOpen}
        tokens={tokens}
      />
    </div>
  );
}
