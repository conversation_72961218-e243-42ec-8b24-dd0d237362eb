import axios from 'axios';

export interface PriceData {
  currency: string;
  date: string;
  price: number;
}

export interface Token {
  symbol: string;
  name: string;
  icon: string;
  balance: string;
  value: string;
  priceUsd: number;
}

const API_BASE_URL = 'https://interview.switcheo.com';

// Create axios instance
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
});

// Currency to icon mapping
const CURRENCY_ICONS: Record<string, string> = {
  ETH: '⟠',
  WBTC: '₿',
  USDC: '💰',
  BUSD: '💰',
  USD: '💵',
  ATOM: '⚛️',
  OSMO: '🧪',
  LUNA: '🌙',
  BLUR: '🔵',
  GMX: '📈',
  OKB: '🟢',
  ZIL: '💎',
  // Add more mappings as needed
};

// Currency to full name mapping
const CURRENCY_NAMES: Record<string, string> = {
  ETH: 'Ethereum',
  WBTC: 'Wrapped Bitcoin',
  USDC: 'USD Coin',
  BUSD: 'Binance USD',
  USD: 'US Dollar',
  ATOM: 'Cosmos',
  OSMO: 'Osmosis',
  LUNA: 'Terra Luna',
  BLUR: 'Blur',
  GMX: 'GMX',
  OKB: 'OKB',
  ZIL: 'Zilliqa',
  bNEO: 'Binance NEO',
  STEVMOS: 'Staked EVMOS',
  RATOM: 'Rocket Pool ATOM',
  STRD: 'Stride',
  EVMOS: 'Evmos',
  IBCX: 'IBC Index',
  IRIS: 'IRISnet',
  ampLUNA: 'Amplified Luna',
  KUJI: 'Kujira',
  STOSMO: 'Staked Osmosis',
  axlUSDC: 'Axelar USDC',
  STATOM: 'Staked ATOM',
  rSWTH: 'Rocket SWTH',
  STLUNA: 'Staked Luna',
  LSI: 'Liquid Staking Index',
  OKT: 'OKT Chain',
  SWTH: 'Switcheo',
  USC: 'USC',
  wstETH: 'Wrapped Staked ETH',
  YieldUSD: 'Yield USD',
};

export const fetchPrices = async (): Promise<PriceData[]> => {
  try {
    const response = await apiClient.get<PriceData[]>('/prices.json');

    if (!Array.isArray(response.data)) {
      throw new Error('Invalid data format received from API');
    }

    return response.data;
  } catch (error) {
    console.error('Error fetching prices:', error);

    if (axios.isAxiosError(error)) {
      if (error.code === 'ECONNABORTED') {
        throw new Error('Request timeout - please check your internet connection');
      }
      if (error.response?.status === 404) {
        throw new Error('Price data not found');
      }
      if (error.response?.status >= 500) {
        throw new Error('Server error - please try again later');
      }
    }

    throw new Error('Failed to fetch price data');
  }
};

export const transformPriceDataToTokens = (priceData: PriceData[]): Token[] => {
  if (!Array.isArray(priceData) || priceData.length === 0) {
    throw new Error('No price data available');
  }

  // Get the latest price for each currency
  const latestPrices = new Map<string, PriceData>();

  priceData.forEach(item => {
    // Validate data structure
    if (!item.currency || typeof item.price !== 'number' || !item.date) {
      console.warn('Invalid price data item:', item);
      return;
    }

    const existing = latestPrices.get(item.currency);
    if (!existing || new Date(item.date) > new Date(existing.date)) {
      latestPrices.set(item.currency, item);
    }
  });

  if (latestPrices.size === 0) {
    throw new Error('No valid price data found');
  }

  // Convert to Token format with mock balance data
  const tokens: Token[] = Array.from(latestPrices.values()).map(priceItem => {
    const mockBalance = Math.random() * 1000; // Random balance for demo
    const totalValue = mockBalance * priceItem.price;

    return {
      symbol: priceItem.currency,
      name: CURRENCY_NAMES[priceItem.currency] || priceItem.currency,
      icon: CURRENCY_ICONS[priceItem.currency] || '🪙',
      balance: mockBalance.toFixed(4),
      value: `$${totalValue.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,
      priceUsd: priceItem.price,
    };
  });

  // Sort by market cap (price * balance) descending
  return tokens.sort((a, b) => {
    const aValue = parseFloat(a.balance) * a.priceUsd;
    const bValue = parseFloat(b.balance) * b.priceUsd;
    return bValue - aValue;
  });
};
