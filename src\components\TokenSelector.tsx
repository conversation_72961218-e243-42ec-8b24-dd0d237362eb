import { useState } from "react";
import { ChevronDown, Search } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { type Token } from "@/services/api";

interface TokenSelectorProps {
  token: Token;
  tokens: Token[];
  onSelect: (token: Token) => void;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export function TokenSelector({
  token,
  tokens,
  onSelect,
  isOpen,
  onOpenChange,
}: TokenSelectorProps) {
  const [searchQuery, setSearchQuery] = useState("");

  const filteredTokens = tokens.filter(
    (t) =>
      t.symbol.toLowerCase().includes(searchQuery.toLowerCase()) ||
      t.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleTokenSelect = (selectedToken: Token) => {
    onSelect(selectedToken);
    onOpenChange(false);
    setSearchQuery("");
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          className="flex items-center gap-2 p-2 hover:bg-gray-700/50 rounded-lg transition-colors"
        >
          <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center text-lg">
            {token.icon}
          </div>
          <span className="font-medium text-white">{token.symbol}</span>
          <ChevronDown className="w-4 h-4 text-gray-400" />
        </Button>
      </DialogTrigger>

      <DialogContent className="bg-gray-800 border-gray-700 text-white max-w-sm">
        <DialogHeader>
          <DialogTitle>Select a token</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              name="searchToken"
              placeholder="Search tokens..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 bg-gray-700 border-gray-600 text-white placeholder:text-gray-400 focus-visible:ring-offset-0 focus-visible:ring-blue-500"
            />
          </div>

          <div className="max-h-64 overflow-y-auto space-y-1">
            {filteredTokens.map((t) => (
              <Button
                key={t.symbol}
                variant="ghost"
                onClick={() => handleTokenSelect(t)}
                className={cn(
                  "w-full justify-start p-3 hover:bg-gray-700 rounded-lg transition-colors",
                  token.symbol === t.symbol && "bg-gray-700"
                )}
              >
                <div className="flex items-center gap-3 w-full">
                  <div className="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center text-lg">
                    {t.icon}
                  </div>
                  <div className="flex-1 text-left">
                    <div className="font-medium">{t.symbol}</div>
                    <div className="text-sm text-gray-400">{t.name}</div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm">{t.balance}</div>
                    <div className="text-xs text-gray-400">{t.value}</div>
                  </div>
                </div>
              </Button>
            ))}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
